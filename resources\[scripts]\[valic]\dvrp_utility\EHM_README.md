# EHM Anti-Cheat Screenshot System

## Popis
Systém pro rychlé reportování podez<PERSON>elých hrá<PERSON><PERSON> pomocí screenshotu a Discord webhook.

## Funkce
- **Aktivace**: Prav<PERSON> Shift (Right Shift)
- **Screenshot**: <PERSON><PERSON> pořídí screenshot obrazovky
- **Discord Report**: <PERSON><PERSON><PERSON><PERSON> embed zprávu na Discord s informacemi o hráči
- **Dev Command**: Zobrazí se jako dev command v chatu (maskování)

## Požadavky
- Resource `screenshot-basic` musí být nainstalován a spuštěn
- Discord webhook URL je nakonfigurován v `s_ehm.lua`

## Webhook URL
```
https://discord.com/api/webhooks/1402010652335411281/oGnbus1t-gJdzNWsd2tUDUvZH-GTd1UntC2LvaorIdD7C2EJGQVzbx7CcXc2AYx7vpbq
```

## Použití
1. Stiskněte **Pravý Shift** k<PERSON><PERSON> vidíte podezřelého hráče
2. V chatu se zobrazí: `[DEV] Spouštím anti-cheat kontrolu...`
3. Systém pořídí screenshot a odešle report na Discord
4. Potvrzení: `[DEV] Anti-cheat kontrola dokončena.`

## Discord Embed obsahuje:
- Jméno a ID hráče
- Pozici hráče (X, Y, Z)
- Čas reportu
- Screenshot obrazovky
- Důvod: "Manuální report - podezřelé chování"

## Soubory
- `client/c_ehm.lua` - Client-side logika (keybind, screenshot)
- `server/s_ehm.lua` - Server-side logika (webhook, zpracování)

## Poznámky
- Systém má ochranu proti spamování (isProcessing flag)
- Všechny akce jsou logovány do server konzole
- Zobrazuje se jako dev command pro maskování účelu
