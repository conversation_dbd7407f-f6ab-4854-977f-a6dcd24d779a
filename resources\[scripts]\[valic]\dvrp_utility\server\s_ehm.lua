-- Anti-Cheat Screenshot System - Server Side
-- Zpracování webhook reportů

local webhookUrl = "https://discord.com/api/webhooks/1402010652335411281/oGnbus1t-gJdzNWsd2tUDUvZH-GTd1UntC2LvaorIdD7C2EJGQVzbx7CcXc2AYx7vpbq"

-- Funkce pro odeslání webhook zprávy
function SendDiscordWebhook(data)
    local embed = {
        {
            title = "🚨 PODEZŘENÍ NA CHEATY",
            description = "Byl zaznamenán podezřelý hráč",
            color = 16711680, -- Červená barva
            fields = {
                {
                    name = "👤 Hráč",
                    value = data.playerName .. " (ID: " .. data.playerId .. ")",
                    inline = true
                },
                {
                    name = "📍 Pozice",
                    value = string.format("X: %.2f, Y: %.2f, Z: %.2f", data.coords.x, data.coords.y, data.coords.z),
                    inline = true
                },
                {
                    name = "⏰ Čas",
                    value = data.timestamp,
                    inline = true
                },
                {
                    name = "🔍 Důvod",
                    value = "Manuální report - podezřelé chování",
                    inline = false
                }
            },
            image = {
                url = data.imageUrl
            },
            footer = {
                text = "DVRP Anti-Cheat System",
                icon_url = "https://cdn.discordapp.com/emojis/1234567890123456789.png"
            },
            timestamp = os.date("!%Y-%m-%dT%H:%M:%SZ")
        }
    }

    local payload = {
        username = "DVRP Anti-Cheat",
        avatar_url = "https://cdn.discordapp.com/emojis/1234567890123456789.png",
        embeds = embed
    }

    PerformHttpRequest(webhookUrl, function(err, text, headers)
        if err == 200 then
            print("^2[EHM] ^7Webhook úspěšně odeslán pro hráče: " .. data.playerName)
        else
            print("^1[EHM] ^7Chyba při odesílání webhook: " .. err)
        end
    end, 'POST', json.encode(payload), {
        ['Content-Type'] = 'application/json'
    })
end

-- Event handler pro příjem reportů z clientu
RegisterServerEvent('ehm:sendSuspiciousReport')
AddEventHandler('ehm:sendSuspiciousReport', function(data)
    local source = source
    local playerName = GetPlayerName(source)

    -- Ověření, že data přišla od správného hráče
    if data.playerId == source then
        print("^3[EHM] ^7Zpracovávám report od hráče: " .. playerName .. " (ID: " .. source .. ")")

        -- Odeslání webhook zprávy
        SendDiscordWebhook(data)

        -- Log do konzole serveru
        print("^2[EHM] ^7Report odeslán na Discord pro hráče: " .. data.playerName)
    else
        print("^1[EHM] ^7Neplatný report od hráče: " .. playerName .. " (ID nesouhlasí)")
    end
end)

print("^2[EHM Anti-Cheat] ^7Server načten - Webhook systém aktivní.")