-- Anti-Cheat Screenshot System
-- Aktivace: Pravý Shift
-- Funkce: Screenshot + Discord webhook report

local isProcessing = false

-- Keybind pro pravý shift (Right Shift = 303)
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)

        -- Kontrola stisku pravého shiftu
        if IsControlJustPressed(0, 303) and not isProcessing then -- Right Shift
            isProcessing = true

            -- Zobrazení dev command zprávy
            TriggerEvent('chat:addMessage', {
                color = {255, 165, 0},
                multiline = true,
                args = {"[DEV]", "Spouštím anti-cheat kontrolu..."}
            })

            -- Malé zpoždění pro realistický efekt
            Citizen.Wait(1000)

            -- Získání informací o hráči
            local playerPed = PlayerPedId()
            local playerId = PlayerId()
            local playerName = GetPlayerName(playerId)
            local playerCoords = GetEntityCoords(playerPed)

            -- Screenshot
            exports['screenshot-basic']:requestScreenshotUpload('https://discord.com/api/webhooks/1402010652335411281/oGnbus1t-gJdzNWsd2tUDUvZH-GTd1UntC2LvaorIdD7C2EJGQVzbx7CcXc2AYx7vpbq', 'files[]', function(data)
                local resp = json.decode(data)
                if resp and resp.attachments and resp.attachments[1] then
                    local imageUrl = resp.attachments[1].url

                    -- Odeslání dat na server pro webhook
                    TriggerServerEvent('ehm:sendSuspiciousReport', {
                        playerName = playerName,
                        playerId = GetPlayerServerId(playerId),
                        coords = playerCoords,
                        imageUrl = imageUrl,
                        timestamp = os.date('%Y-%m-%d %H:%M:%S')
                    })

                    -- Potvrzení pro hráče
                    TriggerEvent('chat:addMessage', {
                        color = {0, 255, 0},
                        multiline = true,
                        args = {"[DEV]", "Anti-cheat kontrola dokončena."}
                    })
                else
                    -- Chyba při screenshotu
                    TriggerEvent('chat:addMessage', {
                        color = {255, 0, 0},
                        multiline = true,
                        args = {"[DEV]", "Chyba při vytváření screenshotu."}
                    })
                end

                isProcessing = false
            end)
        end
    end
end)

print("^2[EHM Anti-Cheat] ^7Client načten - Pravý Shift pro report.")